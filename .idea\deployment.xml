<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" autoUpload="Always" serverName="root-nuist@121.43.36.206:6000 password" remoteFilesAllowedToDisappearOnAutoupload="false">
    <serverData>
      <paths name="root-nuist@121.43.36.206:6000 password">
        <serverdata>
          <mappings>
            <mapping deploy="/OCR_IMG/yxc/kdd25/LLM-SRec-master" local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="root-nuist@121.43.36.206:6000 password (2)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:15334 password">
        <serverdata>
          <mappings>
            <mapping deploy="/root/autodl-tmp/llm" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:15334 password (2)">
        <serverdata>
          <mappings>
            <mapping deploy="/tmp/pycharm_project_243" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="<EMAIL>:28117 password">
        <serverdata>
          <mappings>
            <mapping deploy="/root/autodl-tmp/.autodl" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
    </serverData>
    <option name="myAutoUpload" value="ALWAYS" />
  </component>
</project>